/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 * 本软件源代码版权归京东所有,未经许可不得任意复制与传播.
 */
package com.jdx.rover.server.business.service.hardware;

import com.fasterxml.jackson.core.type.TypeReference;
import com.jd.jmq.common.message.Message;
import com.jdx.rover.common.utils.JsonUtils;
import com.jdx.rover.hardware.data.Connectivity;
import com.jdx.rover.hardware.data.DomainData;
import com.jdx.rover.hardware.data.HardwareData;
import com.jdx.rover.hardware.data.MessageType;
import com.jdx.rover.hardware.data.SimData;
import com.jdx.rover.server.api.domain.dto.hardware.data.ConnectivityDTO;
import com.jdx.rover.server.api.domain.dto.hardware.data.DomainDataDTO;
import com.jdx.rover.server.api.domain.dto.hardware.data.SimDataDTO;
import com.jdx.rover.server.api.domain.dto.mqtt.MqttToJmqDTO;
import com.jdx.rover.server.business.config.ProducerTopicProperties;
import com.jdx.rover.server.business.manager.hardware.data.HardwareChassisDataManager;
import com.jdx.rover.server.business.manager.hardware.data.HardwareGnssDataManager;
import com.jdx.rover.server.business.manager.VehicleSimCardManager;
import com.jdx.rover.server.business.manager.VehicleConnectivityAlarmManager;
import com.jdx.rover.server.business.manager.VehicleDomainControllerAlarmManager;
import com.jdx.rover.server.common.utils.convert.ConvertListUtil;
import com.jdx.rover.server.common.utils.proto.ProtoUtils;
import com.jdx.rover.server.repository.jmq.JmqProducerManager;
import com.jdx.rover.server.repository.redis.hardware.data.HardwareConnectivityRepository;
import com.jdx.rover.server.repository.redis.hardware.data.HardwareDomainDataRepository;
import com.jdx.rover.server.repository.redis.hardware.data.HardwareSimDataRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 车辆硬件信息数据管理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-08-25
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class HardwareDataService {
    private final HardwareSimDataRepository hardwareSimDataRepository;
    private final HardwareDomainDataRepository hardwareDomainDataRepository;
    private final HardwareConnectivityRepository hardwareConnectivityRepository;
    private final HardwareChassisDataManager hardwareChassisDataManager;
    private final HardwareGnssDataManager hardwareGnssDataManager;
    private final VehicleSimCardManager vehicleSimCardManager;
    private final VehicleConnectivityAlarmManager vehicleConnectivityAlarmManager;
    private final VehicleDomainControllerAlarmManager vehicleDomainControllerAlarmManager;
    /**
     * jmq消息发送producer
     */
    private final JmqProducerManager jmqProducerManager;

    /**
     * 生产主题配置类
     */
    private final ProducerTopicProperties producerTopicProperties;


    /**
     * 接收并处理JMQ消息
     *
     * @param message JMQ消息
     */
    public void receive(Message message) {
        try {
            MqttToJmqDTO<byte[]> jmqTopicDTO = JsonUtils.readByteValue(message.getByteBody(), new TypeReference<>() {
            });
            if (Objects.isNull(jmqTopicDTO) || Objects.isNull(jmqTopicDTO.getData())) {
                log.warn("接收到空的JMQ消息");
                return;
            }
            HardwareData hardwareData = HardwareData.parseFrom(jmqTopicDTO.getData());
            processHardwareData(hardwareData);
            log.info("接收到硬件实时数据: {}", ProtoUtils.protoToJson(hardwareData));
        } catch (Exception e) {
            log.error("解析硬件信息数据失败: {}", message, e);
        }
    }

    /**
     * 处理硬件实时数据
     *
     * @param hardwareData 硬件实时数据
     */
    private void processHardwareData(HardwareData hardwareData) {
        try {
            String vehicleName = hardwareData.getHeader().getSendName();
            Date recordTime = new Date(hardwareData.getHeader().getRequestTime());

            if (!StringUtils.hasText(vehicleName)) {
                log.warn("硬件实时数据中的车辆名称为空");
                return;
            }

            // 根据消息类型处理不同的数据
            processDataByMessageTypes(hardwareData, vehicleName, recordTime);
        } catch (Exception e) {
            log.error("处理硬件实时数据失败: {}", hardwareData.getHeader().getSendName(), e);
        }
    }

    /**
     * 根据消息类型处理不同的数据
     *
     * @param hardwareData 硬件实时数据
     * @param vehicleName  车辆名称
     * @param recordTime   记录时间
     */
    private void processDataByMessageTypes(HardwareData hardwareData, String vehicleName, Date recordTime) {
        List<MessageType> messageTypes = hardwareData.getMessageTypeList();
        if (CollectionUtils.isEmpty(messageTypes)) {
            return;
        }

        for (MessageType messageType : messageTypes) {
            try {
                switch (messageType) {
                    case SIM_DATA -> processSimData(hardwareData.getSimDataList(), vehicleName, recordTime);
                    case GNSS_DATA ->
                            hardwareGnssDataManager.processGnssData(hardwareData.getGnssDataList(), vehicleName, recordTime);
                    case DOMAIN_DATA -> processDomainData(hardwareData.getDomainDataList(), vehicleName, recordTime);
                    case CHASSIS_DATA ->
                            hardwareChassisDataManager.processChassisData(hardwareData.getChassisDataList(), vehicleName, recordTime);
                    case CONNECTIVITY ->
                            processConnectivity(hardwareData.getConnectivityList(), vehicleName, recordTime);
                    default -> {
                    }
                }
            } catch (Exception e) {
                log.error("处理消息类型 {} 失败: {}", messageType, vehicleName, e);
            }
        }
    }

    /**
     * 处理SIM卡实时数据
     *
     * @param simDataList SIM卡实时数据列表
     * @param vehicleName 车辆名称
     * @param recordTime  记录时间
     */
    private void processSimData(List<SimData> simDataList, String vehicleName, Date recordTime) {
        if (CollectionUtils.isEmpty(simDataList)) {
            return;
        }

        List<SimDataDTO> dtoList = ConvertListUtil.convertList(simDataList, item -> convertSimData(item, recordTime, vehicleName));
        if (!dtoList.isEmpty()) {
            hardwareSimDataRepository.set(vehicleName, dtoList);
            jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerHardwareSimData(), JsonUtils.writeValueAsString(dtoList), vehicleName);

            // 处理SIM卡异常告警
            try {
                vehicleSimCardManager.handleSimCardAbnormal(vehicleName, null, dtoList, recordTime);
            } catch (Exception e) {
                log.error("处理SIM卡异常告警失败，车辆：{}", vehicleName, e);
            }
        }
    }

    /**
     * 处理域控实时数据
     *
     * @param domainDataList 域控实时数据列表
     * @param vehicleName    车辆名称
     * @param recordTime     记录时间
     */
    private void processDomainData(List<DomainData> domainDataList, String vehicleName, Date recordTime) {
        if (CollectionUtils.isEmpty(domainDataList)) {
            return;
        }

        List<DomainDataDTO> dtoList = ConvertListUtil.convertList(domainDataList, item -> convertDomainData(item, recordTime, vehicleName));
        if (!dtoList.isEmpty()) {
            hardwareDomainDataRepository.set(vehicleName, dtoList);
            jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerHardwareDomainData(), JsonUtils.writeValueAsString(dtoList), vehicleName);

            // 处理域控告警
            try {
                vehicleDomainControllerAlarmManager.handleDomainControllerAlarm(vehicleName, dtoList, recordTime);
            } catch (Exception e) {
                log.error("处理域控告警失败，车辆：{}", vehicleName, e);
            }
        }
    }

    /**
     * 处理连通性数据
     *
     * @param connectivityList 连通性数据列表
     * @param vehicleName      车辆名称
     * @param recordTime       记录时间
     */
    private void processConnectivity(List<Connectivity> connectivityList, String vehicleName, Date recordTime) {
        if (CollectionUtils.isEmpty(connectivityList)) {
            return;
        }

        List<ConnectivityDTO> dtoList = ConvertListUtil.convertList(connectivityList, item -> convertConnectivity(item, recordTime, vehicleName));
        if (!dtoList.isEmpty()) {
            hardwareConnectivityRepository.set(vehicleName, dtoList);
            jmqProducerManager.sendOrderedAndLog(producerTopicProperties.getServerHardwareConnectivity(), JsonUtils.writeValueAsString(dtoList), vehicleName);

            // 处理连通性异常告警
            try {
                vehicleConnectivityAlarmManager.handleConnectivityAlarm(vehicleName, dtoList, recordTime);
            } catch (Exception e) {
                log.error("处理连通性异常告警失败，车辆：{}", vehicleName, e);
            }
        }
    }

    /**
     * 转换SIM卡实时数据
     *
     * @param simData     SIM卡实时数据
     * @param recordTime  记录时间
     * @param vehicleName 车辆名称
     * @return SIM卡实时数据DTO
     */
    private SimDataDTO convertSimData(SimData simData, Date recordTime, String vehicleName) {
        if (simData == null) {
            return null;
        }

        return SimDataDTO.builder()
                .vehicleName(vehicleName)
                .standard(simData.getStandard())
                .band(simData.getBand())
                .communityId(simData.getCommunityId())
                .stationId(simData.getStationId()).rsrp(simData.getRsrp())
                .sinr(simData.getSinr())
                .dailyUpload(simData.getDailyUpload())
                .dailyDownload(simData.getDailyDownlaod())
                .deviceId(simData.getDeviceId())
                .recordTime(recordTime)
                .build();
    }

    /**
     * 转换域控实时数据
     *
     * @param domainData  域控实时数据
     * @param recordTime  记录时间
     * @param vehicleName 车辆名称
     * @return 域控实时数据DTO
     */
    private DomainDataDTO convertDomainData(DomainData domainData, Date recordTime, String vehicleName) {
        if (domainData == null) {
            return null;
        }

        return DomainDataDTO.builder()
                .vehicleName(vehicleName)
                .socTemperature(domainData.getSocTemperature())
                .cpuLoad(domainData.getCpuLoad())
                .memoryUsageRate(domainData.getMemoryUsageRate())
                .networkDelay(domainData.getNetworkDelay())
                .diskUsageRate(domainData.getDiskUsageRate())
                .diskTemperature(domainData.getDiskTemperature())
                .deviceId(domainData.getDeviceId())
                .recordTime(recordTime)
                .build();
    }

    /**
     * 转换连通性信息数据
     *
     * @param connectivity 连通性信息数据
     * @param recordTime   记录时间
     * @param vehicleName  车辆名称
     * @return 连通性信息数据DTO
     */
    private ConnectivityDTO convertConnectivity(Connectivity connectivity, Date recordTime, String vehicleName) {
        if (connectivity == null) {
            return null;
        }

        return ConnectivityDTO.builder()
                .vehicleName(vehicleName)
                .connectivityType(connectivity.getConnectivityType().getNumber())
                .from(connectivity.getFrom())
                .to(connectivity.getTo())
                .delay(connectivity.getDelay())
                .packetLossRate(connectivity.getPacketLossRate())
                .recordTime(recordTime)
                .build();
    }
}