/*
 * Copyright (c) 2025 www.jd.com All rights reserved.
 */
package com.jdx.rover.server.business.manager.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 域控数据记录
 * 用于在Redis中存储域控数据的数值和对应的时间戳
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-09-03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DomainDataRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 数据值（如温度、CPU负载率等）
     */
    private Float value;

    /**
     * 记录时间（上报的recordTime）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 创建域控数据记录
     *
     * @param value 数据值
     * @param recordTime 记录时间
     * @return 域控数据记录
     */
    public static DomainDataRecord of(Float value, Date recordTime) {
        return DomainDataRecord.builder()
                .value(value)
                .recordTime(recordTime)
                .build();
    }

    /**
     * 检查数据值是否大于等于阈值
     *
     * @param threshold 阈值
     * @return true-大于等于阈值，false-小于阈值或值为null
     */
    public boolean isAboveThreshold(Float threshold) {
        return value != null && threshold != null && value >= threshold;
    }

    /**
     * 检查数据值是否小于阈值
     *
     * @param threshold 阈值
     * @return true-小于阈值，false-大于等于阈值或值为null
     */
    public boolean isBelowThreshold(Float threshold) {
        return value != null && threshold != null && value < threshold;
    }
}
